import hashlib
import hmac
import time
from http.client import H<PERSON><PERSON><PERSON>x<PERSON>
from typing import Optional

from blitzy_utils.logger import logger
from cachetools import TTLCache
from flask import Blueprint, jsonify, request
from flask_utils.decorators import flask_pydantic_response

from src.api.handlers.installation_event_handler import \
    handle_installation_event
from src.api.handlers.pr_event_handler import handle_pr_event
from src.api.handlers.repository_event_handler import handle_repository_event
from src.api.routes.operations import operations_bp
from src.api.routes.repositories import repositories_bp
from src.api.routes.secret_manager import secret_bp
from src.api.routes.users import users_bp
from src.consts import GITHUB_WEBHOOK_SECRET
from src.error.errors import ResourceNotFound
from src.service.azure_service import fetch_azure_secret, token_is_expired
from src.service.git_installation_service import \
    get_active_installation_by_target_id
from src.service.github_installation_access_service import \
    get_github_project_repo_by_id

SIGNATURE_HEADER_KEY = "X-Hub-Signature-256"
github_bp = Blueprint("github_bp", __name__, url_prefix="/v1/github")

# Token cache with 55-minute TTL (tokens expire in 1 hour, we refresh 5 minutes early)
token_cache: TTLCache = TTLCache(maxsize=500, ttl=3300)

github_bp.register_blueprint(users_bp)
github_bp.register_blueprint(secret_bp)
github_bp.register_blueprint(repositories_bp)
github_bp.register_blueprint(operations_bp)


def get_cached_azure_token(installation_id: str) -> Optional[str]:
    """
    Get a cached Azure access token if it exists and is not expired.

    :param installation_id: The installation ID to get the token for
    :return: Valid access token if available, None otherwise
    """
    cache_key = f"azure_token_{installation_id}"
    cached_data = token_cache.get(cache_key)

    if cached_data is None:
        logger.debug(f"No cached token found for installation_id: {installation_id}")
        return None

    access_token, _ = cached_data

    try:
        # Check if the token is expired (with 5-minute buffer)
        if token_is_expired(access_token, buffer_seconds=300):
            logger.debug(f"Cached token expired for installation_id: {installation_id}")
            # Remove expired token from cache
            token_cache.pop(cache_key, None)
            return None

        logger.debug(f"Using cached valid token for installation_id: {installation_id}")
        return access_token

    except Exception as e:
        logger.warning(f"Error checking token expiration for installation_id: {installation_id}: {e}")
        # Remove problematic token from cache
        token_cache.pop(cache_key, None)
        return None


def cache_azure_token(installation_id: str, access_token: str) -> None:
    """
    Cache an Azure access token with current timestamp.

    :param installation_id: The installation ID to cache the token for
    :param access_token: The access token to cache
    """
    cache_key = f"azure_token_{installation_id}"
    token_cache[cache_key] = (access_token, time.time())
    logger.debug(f"Cached token for installation_id: {installation_id}")


def get_azure_token_with_cache(installation_id: str) -> str:
    """
    Get Azure access token with caching to avoid unnecessary token exchanges.

    :param installation_id: The installation ID to get the token for
    :return: Valid access token
    :raises: Any exception from fetch_azure_secret if token retrieval fails
    """
    # First, try to get a cached valid token
    cached_token = get_cached_azure_token(installation_id)
    if cached_token:
        return cached_token

    # No valid cached token, fetch a new one
    logger.info(f"Fetching new Azure token for installation_id: {installation_id}")
    secret = fetch_azure_secret(installation_id)
    access_token = secret.accessToken

    # Cache the new token
    cache_azure_token(installation_id, access_token)

    return access_token


@github_bp.route("/webhook", methods=["POST"])
def webhook():
    # Print all headers
    signature = request.headers.get(SIGNATURE_HEADER_KEY)

    verify_signature(request.data, signature)
    logger.debug("Verified signature.")

    event_type = request.headers.get("X-GitHub-Event")
    event_handler_factory(event_type, payload=request.json)

    return jsonify({"status": "success"}), 200


def verify_signature(payload_body: bytes, signature_header: str):
    """
    Verify that the payload was sent from GitHub by validating SHA256. Raise and return 403 if not authorized.

    :param payload_body: The original request body to verify.
    :param signature_header: The signature header received from GitHub.
    """
    if not signature_header:
        raise HTTPException(status_code=403, detail=f"{SIGNATURE_HEADER_KEY} header is missing!")

    expected_signature = generate_expected_signature(payload_body, GITHUB_WEBHOOK_SECRET)

    if not hmac.compare_digest(expected_signature, signature_header):
        raise HTTPException(status_code=403, detail="Signature mismatch! The request could not be verified.")


def generate_expected_signature(payload_body: bytes, secret: str) -> str:
    """Generate the expected HMAC SHA256 signature."""
    generated_hash = hmac.new(secret.encode("utf-8"), msg=payload_body, digestmod=hashlib.sha256)
    return "sha256=" + generated_hash.hexdigest()


def event_handler_factory(event: str, payload: dict):
    """Factory function to create event handlers."""
    logger.info(f"Handling github event {event}")
    if event == "installation":
        handle_installation_event(payload)
    elif event == "repository":
        handle_repository_event(payload)
    elif event == "pull_request":
        handle_pr_event(payload)
    else:
        logger.warning(f"No handler for event {event}")
        logger.debug(f"Payload: {payload}")


@github_bp.route("/repositories/<github_project_repo_id>/access-token", methods=["GET"])
@flask_pydantic_response
def get_access_token_by_installation_id(github_project_repo_id: str):
    """Get complete GitHub installation information with organization details using map_to_model pattern."""
    github_project_repo = get_github_project_repo_by_id(github_project_repo_id)
    if not github_project_repo:
        raise ResourceNotFound(f"No Github project repo found for id: {github_project_repo_id}")

    org_id = github_project_repo.org_id

    # Check if org_id is null or empty
    if org_id is None or org_id.strip() == "":
        logger.error(f"org ID is null or empty for github_project_repo_id: {github_project_repo_id}")
        raise ResourceNotFound(f"organization ID is missing for repository: {github_project_repo_id}")

    # do not use installation_id from github_project_repo table.
    installation = get_active_installation_by_target_id(org_id)
    if not installation:
        logger.error(f"No active installation found for org_id: {org_id}")
        raise ResourceNotFound(f"No active installation found for org_id: {org_id}")
    installation_id = installation.installation_id

    # Check if installation_id is null or empty
    if installation_id is None or installation_id.strip() == "":
        logger.error(f"Installation ID is null or empty for github_project_repo_id: {github_project_repo_id}")
        raise ResourceNotFound(f"Installation ID is missing for repository: {github_project_repo_id}")

    logger.info(f"Fetching access token for installation_id: {installation_id}, org_id: {org_id}")

    # Use cached token approach to avoid unnecessary token exchanges
    access_token = get_azure_token_with_cache(installation_id)

    try:
        response = {
            "access_token": access_token,
            "organization": github_project_repo.org_name,
            "installation_id": installation_id,
            "org_id": org_id,
            "repo_id": github_project_repo.repo_id,
            "repo_name": github_project_repo.repo_name,
            "azure_org_id": github_project_repo.azure_org_id,
            "azure_project_id": github_project_repo.azure_project_id,
            "project_id": github_project_repo.project_id,
            "id": github_project_repo.id
            }

        return response, 200
    except Exception as e:
        logger.error(f"Failed to resolve organization ID: {org_id}. Error: {str(e)}")
        raise
